

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { NumberInput } from './components/NumberInput';
import { ResultsCard } from './components/ResultsCard';
import { CollapsibleSection } from './components/ui/CollapsibleSection';
import { CumulativeSubscriptionLtvChart } from './components/charts/CumulativeSubscriptionLtvChart';
import { RevenueBreakdownChart } from './components/charts/RevenueBreakdownChart';
import { CumulativeFinancialChart } from './components/charts/CumulativeFinancialChart';
import { EfficiencyMetricsChart } from './components/charts/EfficiencyMetricsChart';
import { SubscriberGrowthChart } from './components/charts/SubscriberGrowthChart';
import { ScenarioComparisonTable } from './components/ScenarioComparisonTable'; // Overview table
import { MultiScenarioMetricsTable } from './components/MultiScenarioMetricsTable'; // New detailed comparison table
import { RoasTimeline<PERSON><PERSON> } from './components/charts/RoasTimelineChart';
import { CohortRetentionChart } from './components/charts/CohortRetentionChart';
import { EmailPerformanceChart } from './components/charts/EmailPerformanceChart';
import { PhasePerformanceChart } from './components/charts/PhasePerformanceChart';
import { PpcBenchmarkCheatSheet } from './components/PpcBenchmarkCheatSheet'; // Static Top Nav
import { NativeBenchmarkCheatSheet } from './components/NativeBenchmarkCheatSheet'; // Static Top Nav
import { BenchmarkCheatsheet } from './components/BenchmarkCheatsheet'; // New Dynamic one
import { SimulatorView } from './components/views/SimulatorView'; // New Simulator Page/View
import { SaveScenarioForm } from './components/SaveScenarioForm'; // Import the new component
import { calculateMetricsForPeriod } from './utils/calculations'; // Import from new location

import type {
  CalculatorInputs, CalculatedOverallMetrics, CpmCpcMode,
  CumulativeSubLtvDataPoint, ScenarioID, ScenarioDefinition, ScenarioModifier,
  RevenueBreakdownDataPoint, CumulativeFinancialDataPoint, EfficiencyMetricDataPoint,
  SubscriberGrowthDataPoint, RoasTimelineDataPoint,
  CohortRetentionDataPoint, EmailPerformanceDataPoint,
  SavedScenario, ScenarioComparisonDataPoint, ToggleProps, NumberInputProps,
  CampaignPhase, CampaignPhaseOverrideInputs, PhaseResult, PhasePerformanceDataPoint, ApplicationMode,
  UnifiedIndustry, BenchmarkSet, ChartComparisonDataset, PredefinedPhasedScenario, CurrentView, PrebuiltReportDefinition // Ensured PredefinedPhasedScenario is imported
} from './types';
import { UNIFIED_BENCHMARKS } from './data/unifiedBenchmarkData';
import { AVAILABLE_INDUSTRIES, DEFAULT_INDUSTRY_NATIVE, DEFAULT_INDUSTRY_PPC } from './constants';
import { PREBUILT_REPORTS } from './data/prebuiltReportData';


const MAX_COMPARISON_SCENARIOS = 3;
const COMPARISON_COLORS = ['#fbbf24', '#a3e635', '#f472b6', '#67e8f9']; // amber-400, lime-400, pink-400, cyan-300


// Helper function for debouncing
function sideEffectDebounce(func: (...args: any[]) => void, delay: number) {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;
  return (...args: any[]) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
}


const initialInputsNative: CalculatorInputs = {
  totalAdSpend: 10000,
  dailySpendCap: 500,
  campaignDuration: 30,
  cpmCpcMode: 'cpc',
  cpmValue: 5,
  cpcValue: 0.70,
  impressions: undefined,
  clicks: undefined,
  ctr: 0.5,
  enableMultiStepOffer: false,
  partialCaptureRate: 15,
  websiteConversionRate: 2.5,
  conversions: undefined,
  aovOneTime: 80,
  subscriptionTakeRate: 25,
  aovSubscription: 50,
  avgRebillDuration: 3.5,
  upsellTakeRate: 20,
  avgUpsellValue: 40,
  emailOptInRate: 60,
  emailCvr: 5,
  emailAov: 45,
  cogsPerOrder: 20,
  operationalCosts: 0,
  enablePhasedPlanning: false,
  phases: [],
};

const initialInputsPPC: CalculatorInputs = {
  ...initialInputsNative, // Start with native, then override PPC specifics
  cpmCpcMode: 'cpc', // PPC typically CPC focused
  cpmValue: 0, // Not primary for PPC
  cpcValue: 1.20, // Avg. CPC for PPC might be higher
  ctr: 2.0, // PPC CTR often higher than native display
  websiteConversionRate: 3.0,
  enableMultiStepOffer: false,
  partialCaptureRate: 20,
  operationalCosts: 0,
};


const nativeScenarioDefinitions: ScenarioDefinition[] = [
  { id: 'best', name: 'Best Case', modifiers: [{ field: 'websiteConversionRate', percentageChange: 0.20 }, { field: 'ctr', percentageChange: 0.15 }, { field: 'cpcValue', percentageChange: -0.15 }, { field: 'cpmValue', percentageChange: -0.15 }, { field: 'aovOneTime', percentageChange: 0.10 }, { field: 'subscriptionTakeRate', percentageChange: 0.10 }, { field: 'emailOptInRate', percentageChange: 0.10 }, { field: 'partialCaptureRate', percentageChange: 0.20 }] },
  { id: 'realistic', name: 'Realistic Case', modifiers: [] },
  { id: 'worst', name: 'Worst Case', modifiers: [{ field: 'websiteConversionRate', percentageChange: -0.20 }, { field: 'ctr', percentageChange: -0.15 }, { field: 'cpcValue', percentageChange: 0.15 }, { field: 'cpmValue', percentageChange: 0.15 }, { field: 'aovOneTime', percentageChange: -0.10 }, { field: 'subscriptionTakeRate', percentageChange: -0.10 }, { field: 'emailOptInRate', percentageChange: -0.15 }, { field: 'partialCaptureRate', percentageChange: -0.20 }] },
];

const ppcScenarioDefinitions: ScenarioDefinition[] = [
  { id: 'best', name: 'Best Case (PPC)', modifiers: [{ field: 'websiteConversionRate', percentageChange: 0.25 }, { field: 'ctr', percentageChange: 0.20 }, { field: 'cpcValue', percentageChange: -0.20 }, { field: 'aovOneTime', percentageChange: 0.10 }] },
  { id: 'realistic', name: 'Realistic Case (PPC)', modifiers: [] },
  { id: 'worst', name: 'Worst Case (PPC)', modifiers: [{ field: 'websiteConversionRate', percentageChange: -0.25 }, { field: 'ctr', percentageChange: -0.20 }, { field: 'cpcValue', percentageChange: 0.20 }, { field: 'aovOneTime', percentageChange: -0.10 }] },
];

const PREDEFINED_PHASED_SCENARIOS: PredefinedPhasedScenario[] = [
  // Existing Strategies
  {
    id: 'native-supplement-leadgen-email',
    name: 'Native: Supplement Lead Gen & Email Funnel',
    description: 'Aggressive growth for supplements. Capture leads cheaply, then monetize via a strong email backend. Focus on CPL then Email CVR/AOV.',
    appMode: 'native',
    baseInputsOverrides: {
      enableMultiStepOffer: true, emailCvr: 8, emailAov: 65, subscriptionTakeRate: 35, aovSubscription: 55, avgRebillDuration: 4, cogsPerOrder: 18, operationalCosts: 300,
    },
    phases: [
      { name: 'Test CPL', duration: 15, overrideInputs: { dailySpendCap: 150, cpcValue: 0.45, ctr: 0.7, partialCaptureRate: 25 } },
      { name: 'Optimize Email Backend', duration: 30, overrideInputs: { dailySpendCap: 250, cpcValue: 0.40, ctr: 0.75, partialCaptureRate: 28, websiteConversionRate: 6 } },
      { name: 'Scale Lead Volume', duration: 45, overrideInputs: { dailySpendCap: 500, cpcValue: 0.50, ctr: 0.65, partialCaptureRate: 26, websiteConversionRate: 6.5 } },
    ]
  },
  {
    id: 'ppc-ecom-direct-scale-profit',
    name: 'PPC: E-com Direct Offer Scale (Profit Focus)',
    description: 'Profitably scale a general e-commerce product on PPC (e.g., Google Search/Shopping). Direct offer, focus on ROAS.',
    appMode: 'ppc',
    baseInputsOverrides: {
      enableMultiStepOffer: false, aovOneTime: 75, cogsPerOrder: 25, subscriptionTakeRate: 10, aovSubscription: 40, avgRebillDuration: 3, cpmCpcMode: 'cpc', operationalCosts: 200,
    },
    phases: [
      { name: 'Validate Keywords/Offers', duration: 30, overrideInputs: { dailySpendCap: 200, cpcValue: 1.10, ctr: 3.5, websiteConversionRate: 2.5 } },
      { name: 'Optimize ROAS', duration: 45, overrideInputs: { dailySpendCap: 350, cpcValue: 1.00, ctr: 4.0, websiteConversionRate: 3.0 } },
      { name: 'Scale Profitably', duration: 60, overrideInputs: { dailySpendCap: 600, cpcValue: 1.15, ctr: 3.8, websiteConversionRate: 2.8 } },
    ]
  },
  {
    id: 'native-infoproduct-vsl-optin-sale',
    name: 'Native: Info Product VSL Opt-in & Backend Sale',
    description: 'Drive traffic to a VSL/Advertorial for an info product. Capture opt-ins, then sell via backend (email, webinar).',
    appMode: 'native',
    baseInputsOverrides: {
      enableMultiStepOffer: true, aovOneTime: 147, emailOptInRate: 0, emailCvr: 0, emailAov: 0, cogsPerOrder: 10, operationalCosts: 150,
    },
    phases: [
      { name: 'Test VSL Opt-in', duration: 20, overrideInputs: { dailySpendCap: 200, cpcValue: 0.80, ctr: 0.8, partialCaptureRate: 15 } },
      { name: 'Optimize Backend Sale CVR', duration: 40, overrideInputs: { dailySpendCap: 300, cpcValue: 0.75, ctr: 0.85, partialCaptureRate: 18, websiteConversionRate: 5 } },
      { name: 'Scale VSL Traffic', duration: 60, overrideInputs: { dailySpendCap: 550, cpcValue: 0.90, ctr: 0.70, partialCaptureRate: 16, websiteConversionRate: 5.5 } },
    ]
  },
  {
    id: 'ppc-b2b-leadgen-high-value',
    name: 'PPC: B2B High-Value Lead Gen (CPQL Focus)',
    description: 'Generate qualified leads for a B2B service (e.g., SaaS, consultancy). Focus on lead quality and CPQL.',
    appMode: 'ppc',
    baseInputsOverrides: {
      enableMultiStepOffer: false, websiteConversionRate: 5, aovOneTime: 2000, cogsPerOrder: 200, subscriptionTakeRate: 0, emailOptInRate: 0, cpmCpcMode: 'cpc', operationalCosts: 500,
    },
    phases: [
      { name: 'Initial Lead Quality Test', duration: 30, overrideInputs: { dailySpendCap: 250, cpcValue: 5.00, ctr: 1.5, websiteConversionRate: 4 } },
      { name: 'Refine Targeting for CPQL', duration: 45, overrideInputs: { dailySpendCap: 400, cpcValue: 6.50, ctr: 1.2, websiteConversionRate: 5 } },
      { name: 'Scale Qualified Leads', duration: 60, overrideInputs: { dailySpendCap: 700, cpcValue: 6.00, ctr: 1.3, websiteConversionRate: 5.5 } },
    ]
  },
  {
    id: 'native-skincare-vsl-leadgen-highticket',
    name: 'Native: Skincare - VSL Lead Gen & High-Ticket Backend',
    description: 'Captures leads via a Video Sales Letter for skincare, then converts to a high-value backend offer, common in direct response.',
    appMode: 'native',
    baseInputsOverrides: {
      enableMultiStepOffer: true, aovOneTime: 197, // High-ticket backend AOV
      cogsPerOrder: 40, subscriptionTakeRate: 10, aovSubscription: 97, avgRebillDuration: 2,
      emailCvr: 0, // Assuming sale happens from the primary funnel after lead capture
      operationalCosts: 300,
    },
    phases: [
      { name: 'Lead Gen (VSL Opt-in)', duration: 20, overrideInputs: { dailySpendCap: 200, cpcValue: 0.90, ctr: 0.6, partialCaptureRate: 12 } },
      { name: 'Backend Offer Conversion', duration: 30, overrideInputs: { dailySpendCap: 150, cpcValue: 0.80, ctr: 0.7, partialCaptureRate: 15, websiteConversionRate: 8 } }, // CVR from Lead to Sale
      { name: 'Scale & Optimize LTV', duration: 40, overrideInputs: { dailySpendCap: 250, cpcValue: 0.85, ctr: 0.65, partialCaptureRate: 14, websiteConversionRate: 8.5 } },
    ]
  },
  {
    id: 'native-supplements-direct-roas-max',
    name: 'Native: Supplements - Direct Offer ROAS Maximizer',
    description: 'Focuses on maximizing ROAS for a direct-to-consumer supplement by rigorously testing, optimizing CVR/AOV, and then scaling profit.',
    appMode: 'native',
    baseInputsOverrides: {
      enableMultiStepOffer: false, aovOneTime: 65, cogsPerOrder: 15,
      subscriptionTakeRate: 35, aovSubscription: 45, avgRebillDuration: 4.5,
      emailOptInRate: 50, emailCvr: 6, emailAov: 40,
      operationalCosts: 200,
    },
    phases: [
      { name: 'Test Offer & Creatives', duration: 15, overrideInputs: { dailySpendCap: 100, cpcValue: 0.50, ctr: 0.8, websiteConversionRate: 2.5 } },
      { name: 'Optimize CVR & AOV', duration: 30, overrideInputs: { dailySpendCap: 150, cpcValue: 0.45, ctr: 0.9, websiteConversionRate: 3.5, upsellTakeRate: 25, avgUpsellValue: 30 } },
      { name: 'Scale for Profit', duration: 45, overrideInputs: { dailySpendCap: 300, cpcValue: 0.55, ctr: 0.85, websiteConversionRate: 3.2, upsellTakeRate: 22, avgUpsellValue: 28 } },
    ]
  },
  {
    id: 'native-cbd-gummies-content-funnel',
    name: 'Native: CBD Gummies - Content Funnel & Subscription',
    description: 'Uses a content-driven approach (e.g., advertorial) to educate and sell CBD gummies, with a strong backend emphasis on recurring subscriptions.',
    appMode: 'native',
    baseInputsOverrides: {
      enableMultiStepOffer: false, // Or true if advertorial is lead-gen first
      aovOneTime: 70, cogsPerOrder: 20,
      subscriptionTakeRate: 50, aovSubscription: 50, avgRebillDuration: 5, // High subscription LTV for CBD
      emailOptInRate: 40, emailCvr: 5, emailAov: 45,
      operationalCosts: 280,
    },
    phases: [
      { name: 'Advertorial Test & CPL', duration: 20, overrideInputs: { dailySpendCap: 180, cpcValue: 0.70, ctr: 0.7, websiteConversionRate: 1.5 } }, // If direct, CVR. If leadgen, partialCaptureRate.
      { name: 'Optimize Sales & Subscription', duration: 30, overrideInputs: { dailySpendCap: 250, cpcValue: 0.60, ctr: 0.8, websiteConversionRate: 2.2 } },
      { name: 'Scale Subscriptions', duration: 40, overrideInputs: { dailySpendCap: 400, cpcValue: 0.65, ctr: 0.75, websiteConversionRate: 2.0 } },
    ]
  },
  {
    id: 'native-nutra-try-before-buy',
    name: 'Native: Nutra - "Try Before You Buy" Lead Gen',
    description: 'Offers a physical sample or low-cost trial to generate leads at a low CPL, followed by an upsell to the full product and continuity program.',
    appMode: 'native',
    baseInputsOverrides: {
      enableMultiStepOffer: true, partialCaptureRate: 30, // High opt-in for trial
      aovOneTime: 5, // Cost of trial offer to user (or $0 if free + S&H)
      cogsPerOrder: 10, // COGS for trial
      // Backend sales via emailCvr and emailAov, or websiteConversionRate for step2
      emailCvr: 10, emailAov: 70, // For leads converting to full product
      subscriptionTakeRate: 40, aovSubscription: 60, avgRebillDuration: 3.5, // For full product buyers
      operationalCosts: 320,
    },
    phases: [
      { name: 'Trial Offer CPL Test', duration: 15, overrideInputs: { dailySpendCap: 300, cpcValue: 0.35, ctr: 1.2, partialCaptureRate: 25 } },
      { name: 'Upsell & Backend Monetization', duration: 30, overrideInputs: { dailySpendCap: 200, cpcValue: 0.30, ctr: 1.3, partialCaptureRate: 28, websiteConversionRate: 12 } }, // CVR from Trial to Full
      { name: 'Scale Trial Volume', duration: 30, overrideInputs: { dailySpendCap: 500, cpcValue: 0.40, ctr: 1.1, partialCaptureRate: 22, websiteConversionRate: 10 } },
    ]
  },
  {
    id: 'native-skincare-broad-awareness-retargeting',
    name: 'Native: Skincare - Broad Awareness & Retargeting (Simulated)',
    description: 'Builds a broad audience with engaging content, then "retargets" (simulated via a higher-CVR second phase) with specific offers. Focus on pixel seasoning.',
    appMode: 'native',
    baseInputsOverrides: {
      enableMultiStepOffer: false, aovOneTime: 75, cogsPerOrder: 22,
      subscriptionTakeRate: 20, aovSubscription: 50, avgRebillDuration: 3,
      emailOptInRate: 10, // Lower initial opt-in from broad
      operationalCosts: 260,
    },
    phases: [
      { name: 'Awareness & Pixel Seasoning', duration: 30, overrideInputs: { dailySpendCap: 200, cpmValue: 10, cpmCpcMode: 'cpm', ctr: 0.25, websiteConversionRate: 0.5 } }, // Low CVR on cold
      { name: 'Retargeting & Conversion', duration: 30, overrideInputs: { dailySpendCap: 150, cpcValue: 0.80, cpmCpcMode: 'cpc', ctr: 0.7, websiteConversionRate: 3.5 } }, // Higher CVR on retargeted
      { name: 'Optimize Retargeting LTV', duration: 30, overrideInputs: { dailySpendCap: 180, cpcValue: 0.75, cpmCpcMode: 'cpc', ctr: 0.65, websiteConversionRate: 3.0 } },
    ]
  },
  // PPC Strategies
  {
    id: 'ppc-skincare-high-intent-search',
    name: 'PPC: Skincare - High-Intent Search & Brand Defense',
    description: 'Targets users actively searching for skincare solutions and defends branded keywords, optimizing for direct conversions and ROAS.',
    appMode: 'ppc',
    baseInputsOverrides: {
      cpmCpcMode: 'cpc', enableMultiStepOffer: false, aovOneTime: 90, cogsPerOrder: 28,
      subscriptionTakeRate: 22, aovSubscription: 60, avgRebillDuration: 3.5,
      operationalCosts: 380,
    },
    phases: [
      { name: 'Keyword Discovery & CPA Test', duration: 20, overrideInputs: { dailySpendCap: 150, cpcValue: 1.80, ctr: 4.0, websiteConversionRate: 3.0 } },
      { name: 'Optimize High-Intent & Brand', duration: 40, overrideInputs: { dailySpendCap: 250, cpcValue: 1.50, ctr: 6.0, websiteConversionRate: 5.0 } },
      { name: 'Scale Profitable Keywords', duration: 40, overrideInputs: { dailySpendCap: 400, cpcValue: 1.65, ctr: 5.5, websiteConversionRate: 4.5 } },
    ]
  },
  {
    id: 'ppc-supplements-google-shopping-pmax',
    name: 'PPC: Supplements - Google Shopping & PMax Drive',
    description: 'Focuses on driving supplement sales through optimized Google Shopping feeds and Performance Max campaigns, aiming for efficient CPA and ROAS.',
    appMode: 'ppc',
    baseInputsOverrides: {
      cpmCpcMode: 'cpc', enableMultiStepOffer: false, aovOneTime: 55, cogsPerOrder: 12,
      subscriptionTakeRate: 40, aovSubscription: 40, avgRebillDuration: 5.0,
      operationalCosts: 220,
    },
    phases: [
      { name: 'Feed Opt. & Initial PMax Test', duration: 30, overrideInputs: { dailySpendCap: 100, cpcValue: 0.80, ctr: 1.5, websiteConversionRate: 4.0 } }, // PMax CPC/CTR are blended
      { name: 'Shopping/PMax ROAS Optimization', duration: 45, overrideInputs: { dailySpendCap: 200, cpcValue: 0.70, ctr: 1.8, websiteConversionRate: 5.5 } },
      { name: 'Scale PMax & Shopping', duration: 45, overrideInputs: { dailySpendCap: 350, cpcValue: 0.75, ctr: 1.6, websiteConversionRate: 5.0 } },
    ]
  },
  {
    id: 'ppc-cbd-topicals-educational-display-search',
    name: 'PPC: CBD Topicals - Educational Display to Search Intent',
    description: 'Uses Display/YouTube for initial awareness and education about CBD topicals (lower CPC), then captures users searching with high purchase intent (higher CPC).',
    appMode: 'ppc',
    baseInputsOverrides: {
      cpmCpcMode: 'cpc', enableMultiStepOffer: false, aovOneTime: 75, cogsPerOrder: 25,
      subscriptionTakeRate: 15,
      operationalCosts: 330,
    },
    phases: [
      { name: 'Display/YT Awareness (Low CPC)', duration: 30, overrideInputs: { dailySpendCap: 150, cpcValue: 0.40, ctr: 0.5, websiteConversionRate: 0.8 } }, // Example Display/YT metrics
      { name: 'Search Retargeting & High Intent', duration: 30, overrideInputs: { dailySpendCap: 100, cpcValue: 2.50, ctr: 4.5, websiteConversionRate: 3.5 } }, // Search high intent
      { name: 'Optimize Blended ROAS', duration: 30, overrideInputs: { dailySpendCap: 200, cpcValue: 1.50, ctr: 2.0, websiteConversionRate: 2.5 } }, // Blended optimization
    ]
  },
  {
    id: 'ppc-nutra-discount-promo-scaling',
    name: 'PPC: Nutra - Discount/Promo Driven Scaling',
    description: 'Leverages promotional offers (e.g., % off, BOGO) to aggressively scale sales volume for nutraceutical products, especially during peak seasons or for new launches.',
    appMode: 'ppc',
    baseInputsOverrides: {
      cpmCpcMode: 'cpc', enableMultiStepOffer: false, aovOneTime: 60, // Potentially lower AOV due to discount
      cogsPerOrder: 18, subscriptionTakeRate: 20,
      operationalCosts: 270,
    },
    phases: [
      { name: 'Initial Promo Test (High CVR)', duration: 15, overrideInputs: { dailySpendCap: 200, cpcValue: 0.90, ctr: 5.0, websiteConversionRate: 6.0 } }, // Higher CVR from promo
      { name: 'Scale Promo Volume', duration: 30, overrideInputs: { dailySpendCap: 500, cpcValue: 1.00, ctr: 4.5, websiteConversionRate: 5.5 } },
      { name: 'Post-Promo Sustain & LTV', duration: 30, overrideInputs: { dailySpendCap: 250, cpcValue: 1.10, ctr: 4.0, websiteConversionRate: 3.5 } }, // Return to normal, focus on LTV of acquired
    ]
  },
  {
    id: 'ppc-skincare-problem-solution-nonbrand',
    name: 'PPC: Skincare - Problem/Solution Non-Branded Search',
    description: 'Targets users searching for solutions to specific skincare problems (e.g., "how to reduce wrinkles") with relevant product landing pages, focusing on long-tail keywords.',
    appMode: 'ppc',
    baseInputsOverrides: {
      cpmCpcMode: 'cpc', enableMultiStepOffer: false, aovOneTime: 95, cogsPerOrder: 30,
      subscriptionTakeRate: 18,
      operationalCosts: 410,
    },
    phases: [
      { name: 'Long-Tail Keyword Test (Lower Vol, Higher Intent)', duration: 25, overrideInputs: { dailySpendCap: 100, cpcValue: 1.20, ctr: 3.0, websiteConversionRate: 4.5 } },
      { name: 'Content LP Optimization', duration: 35, overrideInputs: { dailySpendCap: 180, cpcValue: 1.10, ctr: 3.5, websiteConversionRate: 5.5 } }, // Optimize LPs for these queries
      { name: 'Expand & Scale Keywords', duration: 40, overrideInputs: { dailySpendCap: 300, cpcValue: 1.30, ctr: 3.2, websiteConversionRate: 5.0 } },
    ]
  },
  // New Native Strategies
  {
    id: 'native-skincare-influencer-launch',
    name: 'Native: Skincare - Influencer Collaboration Launch',
    description: 'Simulates a skincare product launch leveraging influencer collaborations. Initial phase focuses on awareness, followed by conversion optimization.',
    appMode: 'native',
    baseInputsOverrides: {
      cpmCpcMode: 'cpc', aovOneTime: 85, cogsPerOrder: 25, enableMultiStepOffer: false, operationalCosts: 400,
    },
    phases: [
      { name: 'Influencer Seeding & Buzz', duration: 14, overrideInputs: { dailySpendCap: 300, cpcValue: 0.95, ctr: 0.30, websiteConversionRate: 0.8 } },
      { name: 'Conversion Optimization', duration: 21, overrideInputs: { dailySpendCap: 200, cpcValue: 0.70, ctr: 0.50, websiteConversionRate: 2.5 } },
      { name: 'Sustained Sales & LTV', duration: 30, overrideInputs: { dailySpendCap: 250, cpcValue: 0.75, ctr: 0.45, websiteConversionRate: 2.2, subscriptionTakeRate: 25 } },
    ]
  },
  {
    id: 'native-nutra-content-arbitrage-leadgen',
    name: 'Native: Nutra - Content Arbitrage (Aggressive Lead Gen)',
    description: 'Models a high-volume content arbitrage play for nutraceuticals, focusing on cheap lead generation and backend monetization.',
    appMode: 'native',
    baseInputsOverrides: {
      cpmCpcMode: 'cpc', aovOneTime: 49, cogsPerOrder: 10, enableMultiStepOffer: true, emailCvr: 15, emailAov: 49, operationalCosts: 600,
    },
    phases: [
      { name: 'Click Volume & CPL Test', duration: 10, overrideInputs: { dailySpendCap: 500, cpcValue: 0.15, ctr: 1.0, partialCaptureRate: 8 } },
      { name: 'Lead Quality & Backend CVR Opt.', duration: 15, overrideInputs: { dailySpendCap: 400, cpcValue: 0.18, ctr: 0.9, partialCaptureRate: 10 } }, // Backend conversion via emailCvr
      { name: 'Scale Winning Funnels', duration: 20, overrideInputs: { dailySpendCap: 700, cpcValue: 0.20, ctr: 0.85, partialCaptureRate: 9 } },
    ]
  },
  {
    id: 'native-cbd-geo-targeted-rollout',
    name: 'Native: CBD - Geo-Targeted Rollout & Optimization',
    description: 'Simulates rolling out a CBD campaign by testing in smaller geos first, then expanding to larger, more competitive ones.',
    appMode: 'native',
    baseInputsOverrides: {
      cpmCpcMode: 'cpc', aovOneTime: 110, cogsPerOrder: 30, subscriptionTakeRate: 20, operationalCosts: 350,
    },
    phases: [
      { name: 'Test Geo Group 1 (Lower CPC)', duration: 15, overrideInputs: { dailySpendCap: 100, cpcValue: 0.65, ctr: 0.55, websiteConversionRate: 2.0 } },
      { name: 'Expand Geo Group 2 (Mid CPC)', duration: 20, overrideInputs: { dailySpendCap: 250, cpcValue: 0.85, ctr: 0.50, websiteConversionRate: 2.2 } },
      { name: 'Full Rollout (Higher CPC)', duration: 30, overrideInputs: { dailySpendCap: 450, cpcValue: 1.10, ctr: 0.45, websiteConversionRate: 2.5 } },
    ]
  }
];


const applyScenarioModifiersToInputs = (baseInputs: CalculatorInputs, modifiers: ScenarioModifier[], appMode: ApplicationMode): CalculatorInputs => {
  const newInputs = { ...baseInputs };
  modifiers.forEach(modifier => {
    const originalFieldValue = baseInputs[modifier.field];
    if (typeof originalFieldValue !== 'number' && typeof originalFieldValue !== 'boolean' && modifier.field !== 'cpmCpcMode') {
      console.warn(`Scenario modifier for field ${modifier.field} skipped: original value is not a number, boolean or cpmCpcMode.`);
      return;
    }
    if (appMode === 'native') {
      if (modifier.field === 'cpcValue' && baseInputs.cpmCpcMode === 'cpm') return;
      if (modifier.field === 'cpmValue' && baseInputs.cpmCpcMode === 'cpc') return;
    } else {
      if (modifier.field === 'cpmValue') return;
    }

    if (typeof originalFieldValue === 'number' && modifier.percentageChange !== undefined) {
      let newValue = originalFieldValue * (1 + modifier.percentageChange);
      const fieldsToFloorAtZero = ['cpcValue', 'cpmValue', 'ctr', 'websiteConversionRate', 'aovOneTime', 'subscriptionTakeRate', 'aovSubscription', 'avgRebillDuration', 'upsellTakeRate', 'avgUpsellValue', 'emailOptInRate', 'emailCvr', 'emailAov', 'cogsPerOrder', 'campaignDuration', 'dailySpendCap', 'totalAdSpend', 'partialCaptureRate', 'operationalCosts'];
      if (newValue < 0 && fieldsToFloorAtZero.includes(modifier.field)) newValue = 0;
      const percentageFields = ['ctr', 'websiteConversionRate', 'subscriptionTakeRate', 'upsellTakeRate', 'emailOptInRate', 'emailCvr', 'partialCaptureRate'];
      if (percentageFields.includes(modifier.field)) {
        if (newValue > 100) newValue = 100;
        if (newValue < 0) newValue = 0;
      }
      (newInputs as any)[modifier.field] = parseFloat(newValue.toFixed(4));
    }
  });
  return newInputs;
};


const calculateMetricsAndUpdatePhaseData = (currentInputs: CalculatorInputs, appMode: ApplicationMode): { overallMetrics: CalculatedOverallMetrics, phaseResults: PhaseResult[] } => {
  const emptyMetricsBase = calculateMetricsForPeriod(appMode === 'native' ? initialInputsNative : initialInputsPPC, appMode);
  let phaseResultsOutput: PhaseResult[] = [];

  if (currentInputs.enablePhasedPlanning && currentInputs.phases.length > 0) {
    const aggregatedMetrics: CalculatedOverallMetrics = { ...emptyMetricsBase };
    Object.keys(aggregatedMetrics).forEach(key => (aggregatedMetrics as any)[key] = 0);
    aggregatedMetrics.calculatedImpressions = 0;

    const newPhaseResultsInternal: PhaseResult[] = [];

    currentInputs.phases.forEach(phase => {
      const phaseSpecificInputs: CalculatorInputs = { ...currentInputs };

      Object.keys(phase.overrideInputs).forEach(keyStr => {
        const key = keyStr as keyof CampaignPhaseOverrideInputs;
        if (phase.overrideInputs[key] !== undefined) {
          (phaseSpecificInputs as any)[key] = phase.overrideInputs[key];
        }
      });

      const phaseEffectiveDailySpendCap = phase.overrideInputs.dailySpendCap ?? currentInputs.dailySpendCap;
      const calculatedSpendForThisPhase = (phaseEffectiveDailySpendCap || 0) * phase.duration;

      phaseSpecificInputs.campaignDuration = phase.duration;
      phaseSpecificInputs.totalAdSpend = calculatedSpendForThisPhase;
      phaseSpecificInputs.dailySpendCap = phaseEffectiveDailySpendCap || 0;

      const metricsForThisPhase = calculateMetricsForPeriod(phaseSpecificInputs, appMode);

      newPhaseResultsInternal.push({
        id: phase.id,
        name: phase.name,
        duration: phase.duration,
        calculatedSpendForPhase: calculatedSpendForThisPhase,
        metrics: metricsForThisPhase,
      });

      Object.keys(metricsForThisPhase).forEach(keyStr => {
        const key = keyStr as keyof CalculatedOverallMetrics;
        if (typeof (metricsForThisPhase as any)[key] === 'number' && typeof (aggregatedMetrics as any)[key] === 'number') {
          (aggregatedMetrics as any)[key] += (metricsForThisPhase as any)[key];
        }
      });
    });

    aggregatedMetrics.effectiveCpc = aggregatedMetrics.calculatedClicks > 0 && aggregatedMetrics.totalAdSpend > 0 ? aggregatedMetrics.totalAdSpend / aggregatedMetrics.calculatedClicks : 0;
    aggregatedMetrics.effectiveCpm = aggregatedMetrics.calculatedImpressions > 0 && aggregatedMetrics.totalAdSpend > 0 ? aggregatedMetrics.totalAdSpend / (aggregatedMetrics.calculatedImpressions / 1000) : 0;
    aggregatedMetrics.blendedRoas = aggregatedMetrics.totalAdSpend > 0 ? aggregatedMetrics.totalGrossRevenue / aggregatedMetrics.totalAdSpend : (aggregatedMetrics.totalGrossRevenue > 0 ? Infinity : 0);
    aggregatedMetrics.effectiveCPA = aggregatedMetrics.calculatedConversions > 0 && aggregatedMetrics.totalAdSpend > 0 ? aggregatedMetrics.totalAdSpend / aggregatedMetrics.calculatedConversions : (aggregatedMetrics.totalAdSpend > 0 ? Infinity : 0);
    aggregatedMetrics.totalNetProfit = aggregatedMetrics.totalGrossRevenue - aggregatedMetrics.totalCogs - aggregatedMetrics.totalAdSpend;
    aggregatedMetrics.breakEvenROAS = aggregatedMetrics.totalAdSpend > 0 ? (aggregatedMetrics.totalCogs + aggregatedMetrics.totalAdSpend) / aggregatedMetrics.totalAdSpend : Infinity;
    aggregatedMetrics.profitPerClick = aggregatedMetrics.calculatedClicks > 0 ? aggregatedMetrics.totalNetProfit / aggregatedMetrics.calculatedClicks : (aggregatedMetrics.totalNetProfit !== 0 && aggregatedMetrics.totalAdSpend > 0 ? Infinity * Math.sign(aggregatedMetrics.totalNetProfit) : 0);

    if (aggregatedMetrics.calculatedConversions > 0) {
      const totalNetRevenueFromConversions = aggregatedMetrics.totalRevenueOneTime + aggregatedMetrics.totalRevenueSubscription + aggregatedMetrics.totalRevenueUpsell + aggregatedMetrics.totalRevenueEmail - aggregatedMetrics.totalCogs;
      aggregatedMetrics.netLtvPerAcquiredCustomer = totalNetRevenueFromConversions / aggregatedMetrics.calculatedConversions;
    } else {
      aggregatedMetrics.netLtvPerAcquiredCustomer = 0;
    }
    aggregatedMetrics.ltvCacRatio = aggregatedMetrics.effectiveCPA > 0 && isFinite(aggregatedMetrics.effectiveCPA) && aggregatedMetrics.netLtvPerAcquiredCustomer !== 0 ? aggregatedMetrics.netLtvPerAcquiredCustomer / aggregatedMetrics.effectiveCPA : 0;

    phaseResultsOutput = newPhaseResultsInternal;
    return { overallMetrics: aggregatedMetrics, phaseResults: phaseResultsOutput };
  } else {
    phaseResultsOutput = [];
    return { overallMetrics: calculateMetricsForPeriod(currentInputs, appMode), phaseResults: phaseResultsOutput };
  }
};

// Collapsible state management
const initialAppCollapsibleStates = {
  campaignSettings: true,
  offerSettings: true,
  emailSettings: true,
  costSettings: true,
  campaignPhasing: false,
  performanceDashboard: true,
  visualDashboards: true,
};

const App: React.FC = () => {
  const [currentView, setCurrentView] = useState<CurrentView>('calculator');
  const [appMode, setAppMode] = useState<ApplicationMode>('native');
  const [inputs, setInputs] = useState<CalculatorInputs>(initialInputsNative);
  const [metrics, setMetrics] = useState<CalculatedOverallMetrics | null>(null);

  const [selectedIndustry, setSelectedIndustry] = useState<UnifiedIndustry>(
    localStorage.getItem('roasCalcPro_selectedIndustry_v1') as UnifiedIndustry ||
    (appMode === 'native' ? DEFAULT_INDUSTRY_NATIVE : DEFAULT_INDUSTRY_PPC)
  );

  const [phaseBreakdownData, setPhaseBreakdownData] = useState<PhaseResult[]>([]);
  const [phasePerformanceChartData, setPhasePerformanceChartData] = useState<PhasePerformanceDataPoint[]>([]);

  const [cumulativeSubscriptionLtvData, setCumulativeSubscriptionLtvData] = useState<CumulativeSubLtvDataPoint[]>([]);
  const [revenueBreakdownData, setRevenueBreakdownData] = useState<RevenueBreakdownDataPoint[]>([]);
  const [cumulativeFinancialData, setCumulativeFinancialData] = useState<CumulativeFinancialDataPoint[]>([]);
  const [efficiencyClickData, setEfficiencyClickData] = useState<EfficiencyMetricDataPoint[]>([]);
  const [efficiencyConversionData, setEfficiencyConversionData] = useState<EfficiencyMetricDataPoint[]>([]);
  const [subscriberGrowthData, setSubscriberGrowthData] = useState<SubscriberGrowthDataPoint[]>([]);
  const [roasTimelineData, setRoasTimelineData] = useState<RoasTimelineDataPoint[]>([]);
  const [cohortRetentionData, setCohortRetentionData] = useState<CohortRetentionDataPoint[]>([]);
  const [emailPerformanceData, setEmailPerformanceData] = useState<EmailPerformanceDataPoint[]>([]);

  const [selectedComparisonScenarioIds, setSelectedComparisonScenarioIds] = useState<string[]>([]);
  const [comparisonScenarioDetailsList, setComparisonScenarioDetailsList] = useState<SavedScenario[]>([]);
  const [comparisonChartData, setComparisonChartData] = useState<ScenarioComparisonDataPoint[]>([]);
  const [savedScenarios, setSavedScenarios] = useState<SavedScenario[]>([]);
  const [showSaveScenarioForm, setShowSaveScenarioForm] = useState(false);

  const [appCollapsibleStates, setAppCollapsibleStates] = useState(initialAppCollapsibleStates);

  const [activeScenarioId, setActiveScenarioId] = useState<ScenarioID>('realistic');
  const [originalInputsBeforeScenario, setOriginalInputsBeforeScenario] = useState<CalculatorInputs | null>(null);
  const [showDashboardComparison, setShowDashboardComparison] = useState(false);

  const handleToggleAppCollapsible = useCallback((sectionId: keyof typeof initialAppCollapsibleStates) => {
    setAppCollapsibleStates(prev => ({ ...prev, [sectionId]: !prev[sectionId] }));
  }, []);

  const getLocalStorageKey = useCallback((mode: ApplicationMode) => `roasCalcPro_savedScenarios_${mode}_v1`, []);
  const getCurrentScenarioDefinitions = useCallback(() => appMode === 'native' ? nativeScenarioDefinitions : ppcScenarioDefinitions, [appMode]);
  const getCurrentInitialInputs = useCallback(() => appMode === 'native' ? initialInputsNative : initialInputsPPC, [appMode]);

  useEffect(() => {
    localStorage.setItem('roasCalcPro_selectedIndustry_v1', selectedIndustry);
  }, [selectedIndustry]);

  useEffect(() => {
    const initialModeInputs = getCurrentInitialInputs();
    setInputs(initialModeInputs);
    setOriginalInputsBeforeScenario(null);
    setActiveScenarioId('realistic');
    setSelectedComparisonScenarioIds([]);
    setComparisonScenarioDetailsList([]);
    setShowDashboardComparison(false);

    const defaultIndustryForMode = appMode === 'native' ? DEFAULT_INDUSTRY_NATIVE : DEFAULT_INDUSTRY_PPC;
    const currentIndustryIsValidForMode = AVAILABLE_INDUSTRIES.filter(ind => ind.toLowerCase().startsWith(appMode)).includes(selectedIndustry);
    if (!currentIndustryIsValidForMode) {
      setSelectedIndustry(defaultIndustryForMode);
    }

    const scenarioDefs = getCurrentScenarioDefinitions();
    const defaultScenarios: SavedScenario[] = scenarioDefs.map(scenarioDef => {
      let scenarioSpecificInputs = { ...initialModeInputs };
      if (scenarioDef.id !== 'realistic') {
        scenarioSpecificInputs = applyScenarioModifiersToInputs(initialModeInputs, scenarioDef.modifiers, appMode);
      }
      const { overallMetrics: metricsForThisScenario } = calculateMetricsAndUpdatePhaseData(scenarioSpecificInputs, appMode);
      return {
        id: scenarioDef.id as string,
        name: scenarioDef.name,
        inputsSnapshot: scenarioSpecificInputs,
        metricsSnapshot: metricsForThisScenario,
      };
    });

    let loadedCustomScenarios: SavedScenario[] = [];
    try {
      const storedScenarios = localStorage.getItem(getLocalStorageKey(appMode));
      if (storedScenarios) {
        const parsedScenarios: SavedScenario[] = JSON.parse(storedScenarios);
        loadedCustomScenarios = parsedScenarios.filter(s => !['best', 'realistic', 'worst'].includes(s.id));
      }
    } catch (error) { console.error("Error loading scenarios from localStorage:", error); }
    setSavedScenarios([...defaultScenarios, ...loadedCustomScenarios]);
  }, [appMode, getCurrentInitialInputs, getCurrentScenarioDefinitions, getLocalStorageKey, selectedIndustry]);

  // For now, return a simple component to test the export
  return (
    <div className="min-h-screen bg-slate-900 text-white">
      <header className="bg-slate-800 p-4">
        <h1 className="text-2xl font-bold">ROAS Calculator v4</h1>
        <p>Mode: {appMode}</p>
      </header>
      <main className="p-4">
        <p>App component is working! This is v4 with fixed template literals.</p>
        <button
          onClick={() => setAppMode(appMode === 'native' ? 'ppc' : 'native')}
          className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded mt-4"
        >
          Switch to {appMode === 'native' ? 'PPC' : 'Native'} Mode
        </button>
      </main>
    </div>
  );
};

export default App;